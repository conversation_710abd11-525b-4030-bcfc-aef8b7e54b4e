import React, { useEffect, useRef } from 'react';
import { useData } from '../contexts/DataContext';
import { useTheme } from '../contexts/ThemeContext';
import { Product, CanvasProduct } from '../types';
// @ts-ignore
import jsPDF from 'jspdf';

const InvoicePdfScreen: React.FC = () => {
  const { state } = useData();

  // Use only products added to the current canvas
  const canvasProducts: CanvasProduct[] = state.currentCanvas?.products || [];
  const { isDark } = useTheme();
  const fadeAnim = useRef(0);

  // Helper function to extract base product ID from canvas product ID
  const getBaseProductId = (canvasProductId: string): string => {
    // Canvas product IDs are in format: canvasId_productId_timestamp_randomSuffix
    // We need to extract the original productId
    const parts = canvasProductId.split('_');
    if (parts.length >= 2) {
      // For new format: canvasId_productId_timestamp_randomSuffix
      if (parts.length >= 4) {
        return parts[1]; // Return the original product ID
      }
      // For old format: canvasId_productId
      return parts[1];
    }
    return canvasProductId; // Fallback to the full ID
  };

  // Aggregate products by base product ID and sum quantities
  const aggregatedProducts = canvasProducts.reduce((acc, product) => {
    const baseProductId = getBaseProductId(product.id);

    if (acc[baseProductId]) {
      // Product already exists, increment quantity
      acc[baseProductId].totalQuantity += product.quantity;
      acc[baseProductId].instances += 1;
    } else {
      // First instance of this product
      acc[baseProductId] = {
        ...product,
        baseProductId,
        totalQuantity: product.quantity,
        instances: 1,
      };
    }

    return acc;
  }, {} as Record<string, CanvasProduct & { baseProductId: string; totalQuantity: number; instances: number }>);

  // Convert aggregated products back to array
  const invoiceProducts = Object.values(aggregatedProducts);

  useEffect(() => {
    // Animation placeholder
  }, []);

  const handleGeneratePdf = async () => {
    if (invoiceProducts.length === 0) {
      alert('No products to export. Please add products to the canvas first.');
      return;
    }

    try {
      // Create new PDF document
      const doc = new jsPDF();

      // Set document properties
      doc.setProperties({
        title: 'PlomDesign Invoice',
        subject: 'Product Invoice',
        author: 'PlomDesign Mobile',
        creator: 'PlomDesign Mobile App'
      });

      // Header
      doc.setFontSize(20);
      doc.setFont('helvetica', 'bold');
      doc.text('INVOICE', 105, 20, { align: 'center' });

      // Company info (you can customize this)
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text('PlomDesign Mobile', 20, 35);
      doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, 45);
      doc.text(`Canvas: ${state.currentCanvas?.name || 'Untitled Canvas'}`, 20, 55);

      // Line separator
      doc.line(20, 65, 190, 65);

      // Table headers - New structure with consolidated Item Description and separate pricing columns
      let yPosition = 80;
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Image', 20, yPosition);
      doc.text('Item Description', 55, yPosition);
      doc.text('Unit Price', 130, yPosition);
      doc.text('Quantity', 155, yPosition);
      doc.text('Total Price', 175, yPosition);

      // Header underline
      doc.line(20, yPosition + 2, 190, yPosition + 2);
      yPosition += 25;

      // Products
      doc.setFont('helvetica', 'normal');
      let totalAmount = 0;

      // Process products with images
      for (let i = 0; i < invoiceProducts.length; i++) {
        const product = invoiceProducts[i];

        // Check if we need a new page (account for larger images and multi-line descriptions)
        if (yPosition > 200) {
          doc.addPage();
          yPosition = 30;
        }

        // Add product image if available
        if (product.image) {
          try {
            // Load image and convert to base64
            const img = new Image();
            img.crossOrigin = 'anonymous';

            await new Promise((resolve, reject) => {
              img.onload = () => {
                try {
                  // Create canvas with much higher resolution for crisp images
                  const canvas = document.createElement('canvas');
                  const ctx = canvas.getContext('2d');

                  // Use much higher resolution - 200x200 pixels for very sharp quality
                  const canvasSize = 200;
                  canvas.width = canvasSize;
                  canvas.height = canvasSize;

                  if (ctx) {
                    // Set high-quality rendering settings
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // Fill with white background first
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, canvasSize, canvasSize);

                    // Calculate dimensions to maintain aspect ratio
                    const imgWidth = img.naturalWidth || img.width;
                    const imgHeight = img.naturalHeight || img.height;
                    const aspectRatio = imgWidth / imgHeight;

                    let drawWidth = canvasSize;
                    let drawHeight = canvasSize;
                    let offsetX = 0;
                    let offsetY = 0;

                    if (aspectRatio > 1) {
                      // Image is wider than tall
                      drawHeight = canvasSize / aspectRatio;
                      offsetY = (canvasSize - drawHeight) / 2;
                    } else {
                      // Image is taller than wide
                      drawWidth = canvasSize * aspectRatio;
                      offsetX = (canvasSize - drawWidth) / 2;
                    }

                    // Draw image with proper aspect ratio and centering
                    ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
                  }

                  // Get base64 data with maximum quality
                  const imgData = canvas.toDataURL('image/png', 1.0);

                  // Add image to PDF with larger size for better visibility
                  const pdfImageSize = 30; // Increased size in PDF
                  doc.addImage(imgData, 'PNG', 20, yPosition - 15, pdfImageSize, pdfImageSize);
                  resolve(true);
                } catch (error) {
                  console.warn('Failed to process image for product:', product.name, error);
                  resolve(false);
                }
              };

              img.onerror = () => {
                console.warn('Failed to load image for product:', product.name);
                resolve(false);
              };

              img.src = product.image;
            });
          } catch (error) {
            console.warn('Error processing image for product:', product.name, error);
          }
        } else {
          // Draw placeholder for no image with larger size
          const placeholderSize = 30;
          doc.setFillColor(240, 240, 240);
          doc.rect(20, yPosition - 15, placeholderSize, placeholderSize, 'F');
          doc.setFontSize(9);
          doc.setTextColor(150, 150, 150);
          doc.text('No', 30, yPosition - 5);
          doc.text('Image', 26, yPosition + 2);
          doc.setTextColor(0, 0, 0);
          doc.setFontSize(10);
        }

        // Consolidated Item Description column
        const productName = product.name.length > 20 ? product.name.substring(0, 17) + '...' : product.name;
        const category = product.category || 'N/A';
        const subcategory = product.subcategory || 'N/A';
        const material = product.material || 'N/A';

        // Display consolidated product information with line breaks
        doc.setFontSize(9);
        doc.text(productName, 55, yPosition - 8);
        doc.text(category, 55, yPosition - 2);
        doc.text(subcategory, 55, yPosition + 4);
        doc.text(material, 55, yPosition + 10);
        doc.setFontSize(10);

        // Calculate price for calculations
        const price = product.price && typeof product.price === 'number' ? product.price :
                     product.price && typeof product.price === 'string' && product.price !== '' ? parseFloat(product.price) : 0;

        // Unit Price column (no dollar symbol)
        const unitPriceText = price > 0 ? price.toFixed(2) : '-';
        doc.text(unitPriceText, 130, yPosition);

        // Quantity column
        const qtyText = product.instances > 1 ? `${product.totalQuantity} (${product.instances}x)` : product.totalQuantity.toString();
        doc.text(qtyText, 155, yPosition);

        // Total Price column (unit price × quantity, no dollar symbol)
        const totalPrice = price > 0 ? (price * product.totalQuantity) : 0;
        const totalPriceText = totalPrice > 0 ? totalPrice.toFixed(2) : '-';
        doc.text(totalPriceText, 175, yPosition);

        // Calculate total
        if (price > 0) {
          totalAmount += price * product.totalQuantity;
        }

        // Increase spacing to accommodate multi-line item descriptions
        yPosition += 45;
      }

      // Total section
      yPosition += 10;
      doc.line(20, yPosition, 190, yPosition);
      yPosition += 10;

      doc.setFont('helvetica', 'bold');
      doc.text('TOTAL:', 130, yPosition);
      doc.text(totalAmount.toFixed(2), 175, yPosition);

      // Footer
      yPosition += 20;
      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      doc.text('Generated by PlomDesign Mobile', 105, yPosition, { align: 'center' });
      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, yPosition + 8, { align: 'center' });

      // Save the PDF
      const timestamp = new Date().toISOString().split('T')[0];
      const canvasName = state.currentCanvas?.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'canvas';
      const filename = `PlomDesign_Invoice_${canvasName}_${timestamp}.pdf`;

      doc.save(filename);

      alert(`PDF generated successfully!\nFile: ${filename}`);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  const handleShare = () => {
    const invoiceText = invoiceProducts.map(
      (p, i) =>
        `${i + 1}. ${p.name} (Qty: ${p.totalQuantity}${p.instances > 1 ? ` - ${p.instances} instances` : ''})\nCategory: ${p.category} / ${p.subcategory}\nPrice: ${p.price !== undefined && p.price !== null && (typeof p.price === 'number' || (typeof p.price === 'string' && p.price !== '')) ? p.price : '-'}\nMaterial: ${p.material ? p.material : 'N/A'}`
    ).join('\n\n');
    alert(`Invoice Products:\n\n${invoiceText}`);
  };

  return (
    <div style={{ padding: 0, width: '100%', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ background: '#fff', borderRadius: 16, padding: '24px 0px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)', flex: 1, margin: 0 }}>
        <h3 style={{ fontSize: 20, fontWeight: 600, color: '#2c3e50', marginTop: 0, marginBottom: 0 }}>Products</h3>
        {invoiceProducts.length === 0 ? (
          <p style={{ color: '#888', fontSize: 16, textAlign: 'center', margin: '24px 0' }}>No products on the canvas.</p>
        ) : (
          invoiceProducts.map((product) => (
            <div key={product.baseProductId} style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 18, background: '#f6f8fa', borderRadius: 12, padding: 12 }}>
              {product.image ? (
                <img src={product.image} alt={product.name} style={{ width: 64, height: 64, borderRadius: 8, marginRight: 16, objectFit: 'cover', background: '#eee' }} />
              ) : (
                <div style={{ width: 64, height: 64, borderRadius: 8, background: '#eee', alignItems: 'center', justifyContent: 'center', display: 'flex', marginRight: 16 }}>
                  <span style={{ color: '#aaa' }}>No Image</span>
                </div>
              )}
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: 18, fontWeight: 600, color: '#2c3e50', marginBottom: 4 }}>{product.name}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Category: {product.category} / {product.subcategory}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>
                  Quantity: {product.totalQuantity}
                  {product.instances > 1 && (
                    <span style={{ color: '#6b7280', fontSize: 13, marginLeft: 8 }}>
                      ({product.instances} instances on canvas)
                    </span>
                  )}
                </div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Price: {product.price !== undefined && product.price !== null && (typeof product.price === 'number' || (typeof product.price === 'string' && product.price !== '')) ? product.price : '-'}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Material: {product.material ? product.material : 'N/A'}</div>
              </div>
            </div>
          ))
        )}
        <div style={{ height: 32 }} />
        <div style={{ display: 'flex', gap: 12, justifyContent: 'space-between', padding: '0 12px' }}>
          <button style={{ flex: 1, height: 40, background: '#667eea', borderRadius: 8, color: '#fff', fontSize: 14, fontWeight: 600, border: 'none' }} onClick={handleGeneratePdf}>
            Generate PDF
          </button>
          <button style={{ flex: 1, height: 40, border: '2px solid #667eea', borderRadius: 8, color: '#667eea', fontSize: 14, fontWeight: 600, background: 'none' }} onClick={handleShare}>
            Share Invoice
          </button>
        </div>
      </div>
    </div>
  );
};

export default InvoicePdfScreen;
